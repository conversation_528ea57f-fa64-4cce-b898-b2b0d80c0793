// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import GlobalManagerController, { PageType } from "../../GlobalManagerController";
import LevelSelectController, { LevelStatus } from "./LevelSelectController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelSelectPageController extends cc.Component {

    @property(LevelSelectController)
    levelSelectController: LevelSelectController = null;

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;



    // 新增的游戏按钮
    @property(cc.Button)
    startGameButton: cc.Button = null;

    @property(cc.Button)
    lockedButton: cc.Button = null;

    onLoad() {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();

        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁
        if (this.startGameButton) {
            this.startGameButton.node.active = true;
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = false;
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    }

    start() {
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = (levelNumber: number) => {
                this.onLevelSelectionChanged(levelNumber);
            };
        }

        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态
        this.scheduleOnce(() => {
            this.updateUIDisplay();
        }, 0.1);
    }

    /**
     * 更新UI显示状态
     */
    private updateUIDisplay() {
        // 更新游戏按钮状态
        this.updateGameButtons();
    }



    /**
     * 进入选中的关卡
     */
    public enterSelectedLevel() {
        if (this.levelSelectController) {
            const selectedLevel = this.levelSelectController.getCurrentSelectedLevel();
           

            // 尝试多种方式获取全局管理器
            let globalManager: GlobalManagerController = null;

            // 方法1: 尝试查找 global_node 节点（根目录）
            let globalManagerNode = cc.find("global_node");
            if (globalManagerNode) {
                globalManager = globalManagerNode.getComponent(GlobalManagerController);
          
            } 

            // 方法1.1: 尝试查找 Canvas/global_node 节点
            if (!globalManager) {
                globalManagerNode = cc.find("Canvas/global_node");
                if (globalManagerNode) {
                   
                    globalManager = globalManagerNode.getComponent(GlobalManagerController);
                    if (globalManager) {
                        
                    } else {
                       
                        // 列出节点上的所有组件
                        const components = globalManagerNode.getComponents(cc.Component);
                       
                    }
                } 
            
            }

        


            if (globalManager) {
                console.log(`[LevelSelectPageController] 准备进入关卡，选中的关卡编号: ${selectedLevel}`);

                // 先设置关卡编号，确保在页面切换前就设置好正确的关卡
                if (globalManager.levelPageController) {
                    console.log(`[LevelSelectPageController] 立即设置关卡编号: ${selectedLevel}`);
                    globalManager.levelPageController.setCurrentLevel(selectedLevel);
                }

                // 然后切换到关卡页面
                globalManager.setCurrentPage(PageType.LEVEL_PAGE);

                // 再次确保关卡设置正确（防止页面切换过程中丢失）
                this.scheduleOnce(() => {
                    if (globalManager.levelPageController) {
                        console.log(`[LevelSelectPageController] 确认关卡编号设置: ${selectedLevel}`);
                        globalManager.levelPageController.setCurrentLevel(selectedLevel);
                    }
                }, 0.05); // 缩短延迟时间
            } else {
                cc.error("无法找到 GlobalManagerController 组件！请检查场景配置。");
                cc.error("请确保场景中有节点挂载了 GlobalManagerController 组件。");

                
            }
        }
    }

    /**
     * 设置关卡进度（从外部调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    public setLevelProgress(levelProgressData: any) {
        if (!this.levelSelectController) return;

        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(levelProgressData);

        // 立即更新UI显示，确保按钮状态正确
        this.scheduleOnce(() => {
            this.updateUIDisplay();
        }, 0.05); // 很短的延迟，确保关卡数据已经更新
    }

    /**
     * 修复ScrollView的Scrollbar问题
     */
    private fixScrollViewScrollbar() {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }

        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    }

    /**
     * 更新游戏按钮显示状态
     */
    private updateGameButtons() {
        if (!this.levelSelectController) return;

        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        const levelData = this.levelSelectController.getLevelData(currentLevel);

        if (!levelData) return;

        // 根据关卡状态显示不同的按钮
        const isLocked = levelData.status === LevelStatus.LOCKED;

        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
            // 设置按钮文本 - 统一显示"开始游戏"
            const buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);
            if (buttonLabel) {
                buttonLabel.string = "开始游戏";
            }
        }

        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    }

    /**
     * 开始游戏按钮点击事件
     */
    private onStartGameButtonClick() {
        if (!this.levelSelectController) return;

        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        const levelData = this.levelSelectController.getLevelData(currentLevel);
        console.log(`[LevelSelectPageController] 开始游戏按钮点击，当前选中关卡: ${currentLevel}`);
        console.log(`[LevelSelectPageController] 关卡数据:`, levelData);

        if (levelData && levelData.status !== LevelStatus.LOCKED) {
            console.log(`[LevelSelectPageController] 关卡可用，准备进入游戏`);
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    }

    /**
     * 未解锁按钮点击事件
     */
    private onLockedButtonClick() {
        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();
      
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    }

    /**
     * 关卡选择变化回调
     */
    private onLevelSelectionChanged(levelNumber: number) {

        this.updateUIDisplay();
    }
}
