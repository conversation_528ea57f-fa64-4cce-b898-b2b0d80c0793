
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        console.log("[LevelPageController] \u5F00\u59CB\u6E38\u620F\u6309\u94AE\u70B9\u51FB\uFF0C\u5F53\u524D\u5173\u5361: " + this.currentLevel);
        console.log("[LevelPageController] \u5F53\u524D\u5173\u5361\u6807\u7B7E\u663E\u793A: " + (this.currentLevelLabel ? this.currentLevelLabel.string : 'null'));
        // 使用当前设置的关卡编号，这个值应该是最准确的
        var levelIdToSend = this.currentLevel;
        // 双重检查：如果有关卡标签且已更新，从标签文本中提取关卡编号进行验证
        if (this.currentLevelLabel && this.currentLevelLabel.string) {
            var labelText = this.currentLevelLabel.string;
            // 从"第X关"格式中提取数字
            var match = labelText.match(/第(\d+)关/);
            if (match && match[1]) {
                var labelLevelId = parseInt(match[1]);
                console.log("[LevelPageController] UI\u6807\u7B7E\u663E\u793A\u5173\u5361\u7F16\u53F7: " + labelLevelId);
                // 如果标签和当前关卡不一致，使用标签中的值（UI显示优先）
                if (labelLevelId !== this.currentLevel) {
                    console.warn("[LevelPageController] \u5173\u5361\u7F16\u53F7\u4E0D\u4E00\u81F4\uFF01currentLevel: " + this.currentLevel + ", \u6807\u7B7E\u663E\u793A: " + labelLevelId);
                    levelIdToSend = labelLevelId;
                }
            }
        }
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelId: levelIdToSend
        };
        console.log("[LevelPageController] \u6700\u7EC8\u53D1\u9001ExtendLevelInfo\u8BF7\u6C42\uFF0ClevelId: " + request.levelId);
        console.log("[LevelPageController] \u8BF7\u6C42\u5185\u5BB9:", JSON.stringify(request));
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
            console.log("[LevelPageController] \u66F4\u65B0\u5173\u5361UI\u663E\u793A: " + this.currentLevelLabel.string);
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 先隐藏所有地图容器
        this.hideAllMapContainers();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
            // 检查父节点链是否都是激活状态
            var currentNode = mapNode.parent;
            var parentChain = [];
            while (currentNode) {
                // 避免访问场景节点的 active 属性
                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {
                    parentChain.push(currentNode.name + "(Scene)");
                }
                else {
                    parentChain.push(currentNode.name + "(" + currentNode.active + ")");
                }
                currentNode = currentNode.parent;
            }
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        console.log("[LevelPageController] setCurrentLevel\u88AB\u8C03\u7528\uFF0C\u4F20\u5165\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        this.currentLevel = levelNumber;
        console.log("[LevelPageController] \u8BBE\u7F6EcurrentLevel\u4E3A: " + this.currentLevel);
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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