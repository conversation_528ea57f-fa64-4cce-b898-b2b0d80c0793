
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        console.log("[LevelPageController] \u5F00\u59CB\u6E38\u620F\u6309\u94AE\u70B9\u51FB\uFF0C\u5F53\u524D\u5173\u5361: " + this.currentLevel);
        console.log("[LevelPageController] \u5F53\u524D\u5173\u5361\u6807\u7B7E\u663E\u793A: " + (this.currentLevelLabel ? this.currentLevelLabel.string : 'null'));
        // 从UI标签中提取关卡编号，确保发送的levelId与UI显示完全一致
        var levelIdToSend = this.currentLevel;
        // 如果有关卡标签，从标签文本中提取关卡编号
        if (this.currentLevelLabel && this.currentLevelLabel.string) {
            var labelText = this.currentLevelLabel.string;
            // 从"第X关"格式中提取数字
            var match = labelText.match(/第(\d+)关/);
            if (match && match[1]) {
                levelIdToSend = parseInt(match[1]);
                console.log("[LevelPageController] \u4ECEUI\u6807\u7B7E\u63D0\u53D6\u5173\u5361\u7F16\u53F7: " + levelIdToSend);
            }
        }
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelId: levelIdToSend
        };
        console.log("[LevelPageController] \u53D1\u9001ExtendLevelInfo\u8BF7\u6C42\uFF0ClevelId: " + request.levelId);
        console.log("[LevelPageController] \u8BF7\u6C42\u5185\u5BB9:", JSON.stringify(request));
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
            console.log("[LevelPageController] \u66F4\u65B0\u5173\u5361UI\u663E\u793A: " + this.currentLevelLabel.string);
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 先隐藏所有地图容器
        this.hideAllMapContainers();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
            // 检查父节点链是否都是激活状态
            var currentNode = mapNode.parent;
            var parentChain = [];
            while (currentNode) {
                // 避免访问场景节点的 active 属性
                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {
                    parentChain.push(currentNode.name + "(Scene)");
                }
                else {
                    parentChain.push(currentNode.name + "(" + currentNode.active + ")");
                }
                currentNode = currentNode.parent;
            }
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        console.log("[LevelPageController] setCurrentLevel\u88AB\u8C03\u7528\uFF0C\u4F20\u5165\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        this.currentLevel = levelNumber;
        console.log("[LevelPageController] \u8BBE\u7F6EcurrentLevel\u4E3A: " + this.currentLevel);
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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