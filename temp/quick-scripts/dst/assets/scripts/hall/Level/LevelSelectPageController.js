
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalManagerController_1 = require("../../GlobalManagerController");
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁
        if (this.startGameButton) {
            this.startGameButton.node.active = true;
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = false;
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.1);
    };
    /**
     * 更新UI显示状态
     */
    LevelSelectPageController.prototype.updateUIDisplay = function () {
        // 更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel_1 = this.levelSelectController.getCurrentSelectedLevel();
            // 尝试多种方式获取全局管理器
            var globalManager_1 = null;
            // 方法1: 尝试查找 global_node 节点（根目录）
            var globalManagerNode = cc.find("global_node");
            if (globalManagerNode) {
                globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
            }
            // 方法1.1: 尝试查找 Canvas/global_node 节点
            if (!globalManager_1) {
                globalManagerNode = cc.find("Canvas/global_node");
                if (globalManagerNode) {
                    globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
                    if (globalManager_1) {
                    }
                    else {
                        // 列出节点上的所有组件
                        var components = globalManagerNode.getComponents(cc.Component);
                    }
                }
            }
            if (globalManager_1) {
                console.log("[LevelSelectPageController] \u51C6\u5907\u8FDB\u5165\u5173\u5361\uFF0C\u9009\u4E2D\u7684\u5173\u5361\u7F16\u53F7: " + selectedLevel_1);
                // 先切换到关卡页面
                globalManager_1.setCurrentPage(GlobalManagerController_1.PageType.LEVEL_PAGE);
                // 延迟设置关卡，确保页面切换完成后再设置
                this.scheduleOnce(function () {
                    if (globalManager_1.levelPageController) {
                        console.log("[LevelSelectPageController] \u8C03\u7528setCurrentLevel\uFF0C\u4F20\u9012\u5173\u5361\u7F16\u53F7: " + selectedLevel_1);
                        globalManager_1.levelPageController.setCurrentLevel(selectedLevel_1);
                    }
                }, 0.1);
            }
            else {
                cc.error("无法找到 GlobalManagerController 组件！请检查场景配置。");
                cc.error("请确保场景中有节点挂载了 GlobalManagerController 组件。");
            }
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectPageController.prototype.setLevelProgress = function (levelProgressData) {
        var _this = this;
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(levelProgressData);
        // 立即更新UI显示，确保按钮状态正确
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.05); // 很短的延迟，确保关卡数据已经更新
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
            // 设置按钮文本 - 统一显示"开始游戏"
            var buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);
            if (buttonLabel) {
                buttonLabel.string = "开始游戏";
            }
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        console.log("[LevelSelectPageController] \u5F00\u59CB\u6E38\u620F\u6309\u94AE\u70B9\u51FB\uFF0C\u5F53\u524D\u9009\u4E2D\u5173\u5361: " + currentLevel);
        console.log("[LevelSelectPageController] \u5173\u5361\u6570\u636E:", levelData);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            console.log("[LevelSelectPageController] \u5173\u5361\u53EF\u7528\uFF0C\u51C6\u5907\u8FDB\u5165\u6E38\u620F");
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateUIDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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