
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '40c9e3ykUFClLHG7s1cQEKm', 'LevelSelectController');
// scripts/hall/Level/LevelSelectController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelStatus = void 0;
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 关卡状态枚举
var LevelStatus;
(function (LevelStatus) {
    LevelStatus[LevelStatus["LOCKED"] = 0] = "LOCKED";
    LevelStatus[LevelStatus["CURRENT"] = 1] = "CURRENT";
    LevelStatus[LevelStatus["COMPLETED"] = 2] = "COMPLETED"; // 已通关（绿色）
})(LevelStatus = exports.LevelStatus || (exports.LevelStatus = {}));
var LevelSelectController = /** @class */ (function (_super) {
    __extends(LevelSelectController, _super);
    function LevelSelectController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.screenWidth = 650;
        _this.levelItemWidth = 150; // 关卡项的宽度（包括间距）
        _this.visibleLevels = 3; // 可见关卡数量
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // 关卡节点列表
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        // 关卡选择变化回调
        _this.onLevelSelectionChanged = null;
        return _this;
    }
    LevelSelectController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();
        // 设置初始滚动位置为第1关（最左边）
        if (this.scrollView) {
            this.scrollView.scrollToPercentHorizontal(0, 0);
        }
    };
    LevelSelectController.prototype.start = function () {
        var _this = this;
        // 延迟滚动到当前选中关卡，确保界面完全初始化
        this.scheduleOnce(function () {
            // 滚动到当前选中的关卡（可能已经通过ExtendLevelProgress设置了）
            _this.scrollToLevel(_this.currentSelectedLevel);
        }, 0.1);
    };
    LevelSelectController.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectController.prototype.initLevelData = function () {
        this.levelDataList = [];
        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）
            }
            else {
                status = LevelStatus.LOCKED; // 其他关卡锁定
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
        // 默认选中第一关
        this.currentSelectedLevel = 1;
    };
    /**
     * 创建关卡项目
     */
    LevelSelectController.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectController.prototype.createLevelNode = function (levelData) {
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectController.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectController.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });
        return node;
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelSelectController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectController.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后强制设置正确的大小，覆盖图片原始大小
                node.setContentSize(size);
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新关卡显示
     */
    LevelSelectController.prototype.updateLevelDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectController.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        // 设置自动滚动标志，防止滚动过程中触发位置更新
        this.isAutoScrolling = true;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectController.prototype.onLevelClicked = function (event, customEventData) {
        var levelNumber = parseInt(customEventData);
        console.log("[LevelSelectController] \u5173\u5361\u88AB\u70B9\u51FB\uFF0CcustomEventData: " + customEventData + ", \u89E3\u6790\u540E\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        // 允许选择任何关卡（包括未解锁的）
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        console.log("[LevelSelectController] \u8BBE\u7F6EcurrentSelectedLevel\u4E3A: " + this.currentSelectedLevel);
        this.updateLevelDisplay();
        // 滚动到选中关卡
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(levelNumber);
        }
        // 这里可以添加进入关卡的逻辑
        // this.enterLevel(levelNumber);
    };
    /**
     * 设置关卡状态
     */
    LevelSelectController.prototype.setLevelStatus = function (levelNumber, status) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    };
    /**
     * 获取当前选中的关卡
     */
    LevelSelectController.prototype.getCurrentSelectedLevel = function () {
        return this.currentSelectedLevel;
    };
    /**
     * 获取指定关卡的数据
     */
    LevelSelectController.prototype.getLevelData = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return null;
        return this.levelDataList[levelNumber - 1];
    };
    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectController.prototype.setLevelProgress = function (levelProgressData) {
        if (!levelProgressData) {
            cc.warn("❌ levelProgressData 为空");
            return;
        }
        var clearedLevels = levelProgressData.clearedLevels, currentLevel = levelProgressData.currentLevel, totalLevels = levelProgressData.totalLevels;
        // 验证数据有效性
        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels) {
            cc.warn("\u274C \u6570\u636E\u9A8C\u8BC1\u5931\u8D25: clearedLevels=" + clearedLevels + ", currentLevel=" + currentLevel + ", totalLevels=" + totalLevels);
            return;
        }
        // 更新总关卡数（如果后端传了的话）
        if (totalLevels && totalLevels > 0) {
            this.totalLevels = totalLevels;
        }
        // 重新设置所有关卡状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i < currentLevel) {
                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）
            }
            else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）
            }
            else {
                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）
            }
            this.levelDataList[i - 1].status = status;
        }
        // 更新当前选中关卡为后端指定的currentLevel
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();
        // scrollToLevel 方法内部会设置 isAutoScrolling = true
        this.scrollToLevel(currentLevel);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(currentLevel);
        }
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     */
    LevelSelectController.prototype.setLevelProgressLegacy = function (completedLevels) {
        // 转换为新的数据格式
        var levelProgressData = {
            clearedLevels: completedLevels,
            currentLevel: Math.min(completedLevels + 1, this.totalLevels),
            totalLevels: this.totalLevels
        };
        this.setLevelProgress(levelProgressData);
    };
    /**
     * 解锁下一关
     */
    LevelSelectController.prototype.unlockNextLevel = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectController.prototype.completeCurrentLevel = function () {
        var currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectController.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectController.prototype.onScrolling = function () {
        // 如果是自动滚动，不要更新选中关卡
        if (this.isAutoScrolling) {
            return;
        }
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectController.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectController.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectController.prototype.fixScrollViewScrollbar = function () {
        if (this.scrollView && this.content) {
            ScrollViewHelper_1.ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectController.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 更新标签外边框
     */
    LevelSelectController.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    LevelSelectController.prototype.refreshLevelItems = function () {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectController.prototype, "content", void 0);
    LevelSelectController = __decorate([
        ccclass
    ], LevelSelectController);
    return LevelSelectController;
}(cc.Component));
exports.default = LevelSelectController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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